import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { of, throwError, Subject } from 'rxjs';
import { Grid<PERSON><PERSON> } from 'ag-grid-community';

import { RoleDetailsComponent } from './role-details.component';
import { EditingStateService } from '../../../../services/editing-state.service';
import { RoleService } from '../../../../services/admin/role.service';
import { ToastService } from '../../../../services/toast.service';
import { DialogMessageService } from '../../../../services/dialog-message.service';
import { WorkflowService } from '../../../../services/workflow/workflow.service';
import { IRole } from '../../../../models/admin/roles/role.model';
import { IRoleCapabilities } from '../../../../models/admin/roles/role-capabilities';
import { IRoleTask } from '../../../../models/workflow/role-task';
import { IWorkflowTaskType } from '../../../../models/workflow/workflow-task-type.model';
import { IRoleTaskType } from '../../../../models/admin/roles/role-task-type';
import { FormAction } from '../../../../models/form-action';
import { RoleActions } from '../../../../models/enums/role-actions';

// Mock buildCapabilitiesTree function
jest.mock('../../../../utils/grid-utils', () => ({
  buildCapabilitiesTree: jest.fn(() => [
    [
      { component: ['admin'], view: true, edit: false, create: false, delete: false },
      { component: ['user'], view: true, edit: true, create: true, delete: false }
    ],
    new Map([
      ['admin', 'Administration'],
      ['user', 'User Management']
    ])
  ]),
  handleCancelAction: jest.fn()
}));

describe('RoleDetailsComponent', () => {
  let component: RoleDetailsComponent;
  let fixture: ComponentFixture<RoleDetailsComponent>;
  let mockEditingStateService: jest.Mocked<EditingStateService>;
  let mockRoleService: jest.Mocked<RoleService>;
  let mockToastService: jest.Mocked<ToastService>;
  let mockDialogMessageService: jest.Mocked<DialogMessageService>;
  let mockWorkflowService: jest.Mocked<WorkflowService>;
  let mockRouter: jest.Mocked<Router>;
  let mockActivatedRoute: jest.Mocked<ActivatedRoute>;

  const mockRole: IRole = {
    roleId: '1',
    name: 'Manager',
    department: 'Sales',
    description: 'Sales manager role',
    superiorId: '2',
    superior: {
      roleId: '2',
      name: 'Admin',
      department: 'IT',
      description: 'Admin role',
      capabilities: []
    },
    capabilities: [
      { component: ['admin'], view: true, edit: false, create: false, delete: false },
      { component: ['user'], view: true, edit: true, create: true, delete: false }
    ],
    tasks: [
      { roleId: '1', taskId: 'task1' }
    ]
  };

  const mockRoles: IRole[] = [
    mockRole,
    {
      roleId: '2',
      name: 'Admin',
      department: 'IT',
      description: 'Admin role',
      capabilities: []
    },
    {
      roleId: '3',
      name: 'Employee',
      department: 'Sales',
      description: 'Employee role',
      capabilities: []
    }
  ];

  const mockWorkflowTaskTypes: IWorkflowTaskType[] = [
    {
      taskId: 'task1',
      description: 'Review Task',
      workflow: {
        workflowType: {
          createdBySystem: true
        }
      }
    },
    {
      taskId: 'task2',
      description: 'Approval Task',
      workflow: {
        workflowType: {
          createdBySystem: true
        }
      }
    }
  ];

  beforeEach(async () => {
    const mockEditingStateSubject = new Subject<FormAction>();

    mockEditingStateService = {
      getValue: jest.fn().mockReturnValue(mockEditingStateSubject.asObservable()),
      setValue: jest.fn(),
      setData: jest.fn(),
      editingState: {
        isEditing: false,
        hasChanges: false
      }
    } as any;

    mockRoleService = {
      findAll: jest.fn().mockReturnValue(of(mockRoles)),
      updateCapabilities: jest.fn().mockReturnValue(of(mockRole))
    } as any;

    mockToastService = {
      displaySuccess: jest.fn(),
      displayError: jest.fn()
    } as any;

    mockDialogMessageService = {
      displayError: jest.fn()
    } as any;

    mockWorkflowService = {
      findWorkflowTaskTypes: jest.fn().mockReturnValue(of(mockWorkflowTaskTypes))
    } as any;

    mockRouter = {
      navigate: jest.fn().mockResolvedValue(true)
    } as any;

    mockActivatedRoute = {
      data: of({ data: mockRole }),
      snapshot: { params: {}, queryParams: {} }
    } as any;

    await TestBed.configureTestingModule({
      declarations: [RoleDetailsComponent],
      providers: [
        { provide: EditingStateService, useValue: mockEditingStateService },
        { provide: RoleService, useValue: mockRoleService },
        { provide: ToastService, useValue: mockToastService },
        { provide: DialogMessageService, useValue: mockDialogMessageService },
        { provide: WorkflowService, useValue: mockWorkflowService },
        { provide: Router, useValue: mockRouter },
        { provide: ActivatedRoute, useValue: mockActivatedRoute }
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(RoleDetailsComponent);
    component = fixture.componentInstance;
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default values', () => {
      expect(component.title).toBe('User List');
      expect(component.roles).toEqual([]);
      expect(component.rowData).toEqual([]);
      expect(component.defRowData).toEqual([]);
      expect(component.treeRoles).toEqual([]);
      expect(component.upperRoles).toEqual([]);
      expect(component.defUpperRoles).toEqual([]);
      expect(component.departmentList).toEqual([]);
      expect(component.errors).toEqual([]);
      expect(component.path).toBe(location.pathname);
      expect(component.actions).toBe(RoleActions);
    });

    it('should have correct pathTitleMap', () => {
      expect(component.pathTitleMap).toBeInstanceOf(Map);
    });
  });

  describe('ngOnInit', () => {
    it('should call loadData on initialization', () => {
      jest.spyOn(component as any, 'loadData');

      component.ngOnInit();

      expect(component['loadData']).toHaveBeenCalled();
    });
  });

  describe('Data Loading', () => {
    beforeEach(() => {
      fixture.detectChanges(); // This triggers ngOnInit and loadData
    });

    it('should load role data from route', () => {
      expect(component.role).toEqual(mockRole);
      expect(component.selectedRole).toEqual({
        name: mockRole.name,
        code: mockRole.roleId,
        label: mockRole.name
      });
      expect(component.selectedDepartment).toEqual({
        code: mockRole.department,
        name: mockRole.department
      });
      expect(component.selectedUpperRole).toEqual({
        code: mockRole.superiorId,
        name: mockRole.superior?.name
      });
      expect(component.roleDescription).toBe(mockRole.description);
      expect(component.isAdmin).toBe(false);
    });

    it('should handle Admin role correctly', () => {
      const adminRole = { ...mockRole, name: 'Admin' };
      mockActivatedRoute.data = of({ data: adminRole });

      // Re-create component to trigger new data loading
      fixture = TestBed.createComponent(RoleDetailsComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();

      expect(component.isAdmin).toBe(true);
    });

    it('should load roles and filter out Admin', () => {
      expect(mockRoleService.findAll).toHaveBeenCalled();
      expect(component.roles.length).toBe(2); // Admin should be filtered out
      expect(component.roles.find(r => r.name === 'Admin')).toBeUndefined();
    });

    it('should load workflow task types', () => {
      expect(mockWorkflowService.findWorkflowTaskTypes).toHaveBeenCalled();
      expect(component.workflowRowData).toBeDefined();
      expect(component.workflowRowData.length).toBe(2);
      expect(component.workflowRowData[0].attend).toBe(true); // task1 is in role.tasks
      expect(component.workflowRowData[1].attend).toBe(false); // task2 is not in role.tasks
    });

    it('should handle role service error', () => {
      mockRoleService.findAll.mockReturnValue(throwError('Service error'));

      // Re-create component to trigger new data loading
      fixture = TestBed.createComponent(RoleDetailsComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();

      expect(mockToastService.displayError).toHaveBeenCalledWith('Failed retrieving roles.');
    });
  });

  describe('Column Definitions', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should set column definitions correctly', () => {
      expect(component.columnDefs).toBeDefined();
      expect(component.columnDefs.length).toBe(4);

      expect(component.columnDefs[0].field).toBe('view');
      expect(component.columnDefs[0].headerName).toBe('View');
      expect(component.columnDefs[1].field).toBe('edit');
      expect(component.columnDefs[1].headerName).toBe('Edit');
      expect(component.columnDefs[2].field).toBe('create');
      expect(component.columnDefs[2].headerName).toBe('Create');
      expect(component.columnDefs[3].field).toBe('delete');
      expect(component.columnDefs[3].headerName).toBe('Delete');
    });

    it('should configure auto group column definition', () => {
      expect(component.autoGroupColumnDef).toBeDefined();
      expect(component.autoGroupColumnDef.field).toBe('name');
      expect(component.autoGroupColumnDef.headerName).toBe('Component');
      expect(component.autoGroupColumnDef.minWidth).toBe(300);
    });

    it('should set workflow column definitions correctly', () => {
      expect(component.workflowColumnDefs).toBeDefined();
      expect(component.workflowColumnDefs.length).toBe(2);

      expect(component.workflowColumnDefs[0].field).toBe('task');
      expect(component.workflowColumnDefs[0].headerName).toBe('Task');
      expect(component.workflowColumnDefs[1].field).toBe('attend');
      expect(component.workflowColumnDefs[1].editable).toBe(true);
    });

    it('should configure checkbox renderer for workflow attend field', () => {
      const attendColumn = component.workflowColumnDefs[1];
      expect(attendColumn.cellRenderer).toBeDefined();
      expect(attendColumn.cellRendererParams).toBeDefined();
    });
  });

  describe('Grid Operations', () => {
    it('should handle grid ready event', () => {
      const mockGridApi = { test: 'api' } as any;

      component.onGridIsReady(mockGridApi);

      expect(component.gridApi).toBe(mockGridApi);
    });

    it('should handle workflow grid ready event', () => {
      const mockGridApi = { test: 'workflowApi' } as any;

      component.onWorkflowGridIsReady(mockGridApi);

      expect(component.workflowGridApi).toBe(mockGridApi);
    });

    it('should get data path correctly', () => {
      const mockData = { component: ['admin', 'users'] };

      const result = component.getDataPath(mockData);

      expect(result).toEqual(['admin', 'users']);
    });
  });

  describe('Department Management', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should load department list from roles', () => {
      expect(component.departmentList.length).toBeGreaterThan(0);
      expect(component.departmentList).toContainEqual({ name: 'Sales', code: 'Sales' });
      expect(component.departmentList).toContainEqual({ name: 'IT', code: 'IT' });
    });

    it('should filter departments correctly', () => {
      const mockEvent = { query: 'sal' };

      component.filterDepartment(mockEvent);

      expect(component.suggestedDepartments).toEqual([{ name: 'Sales', code: 'Sales' }]);
    });

    it('should handle empty department filter', () => {
      const mockEvent = { query: '' };

      component.filterDepartment(mockEvent);

      expect(component.suggestedDepartments).toEqual(component.departmentList);
    });
  });

  describe('Role Hierarchy Management', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should load upper roles correctly', () => {
      expect(component.defUpperRoles.length).toBe(2); // Excluding Admin
      expect(component.upperRoles.length).toBe(2);
      expect(component.defUpperRoles[0]).toEqual({ name: 'Manager', code: '1' });
      expect(component.defUpperRoles[1]).toEqual({ name: 'Employee', code: '3' });
    });

    it('should filter upper roles to exclude current role', () => {
      component.selectedRole = { name: 'Manager', code: '1' };
      component.selectedUpperRole = { name: 'Admin', code: '2' };

      component['loadUpperRoles']();

      expect(component.upperRoles.length).toBe(1);
      expect(component.upperRoles.find(r => r.name === 'Manager')).toBeUndefined();
    });

    it('should load tree roles correctly', () => {
      // Mock roles without superiors for tree root
      const rolesWithoutSuperior = [
        { ...mockRoles[1], superior: null }, // Admin
        { ...mockRoles[2], superior: null }  // Employee
      ];
      component.roles = rolesWithoutSuperior;

      component['loadTreeRoles']();

      expect(component.treeRoles.length).toBe(2);
      expect(component.treeRoles[0]).toEqual({ name: 'Admin', code: '2', label: 'Admin' });
    });
  });

  describe('Data Validation', () => {
    beforeEach(() => {
      fixture.detectChanges();
      component.selectedRole = { name: 'Manager', code: '1' };
      component.selectedDepartment = { name: 'Sales', code: 'Sales' };
      component.roleDescription = 'Valid description';
    });

    it('should validate successfully with valid data', () => {
      const result = component['validateData']();

      expect(result).toBe(true);
      expect(component.errors).toEqual([]);
    });

    it('should fail validation for empty role name', () => {
      component.selectedRole = { name: '', code: '1' };

      const result = component['validateData']();

      expect(result).toBe(false);
      expect(component.errors).toContain('Role name is required.');
      expect(mockDialogMessageService.displayError).toHaveBeenCalled();
    });

    it('should fail validation for empty department', () => {
      component.selectedDepartment = null;

      const result = component['validateData']();

      expect(result).toBe(false);
      expect(component.errors).toContain('Department is required.');
    });

    it('should fail validation for empty description', () => {
      component.roleDescription = '';

      const result = component['validateData']();

      expect(result).toBe(false);
      expect(component.errors).toContain('Description is required.');
    });

    it('should fail validation for duplicate role name', () => {
      component.selectedRole = { name: 'Employee', code: '1' }; // Employee already exists

      const result = component['validateData']();

      expect(result).toBe(false);
      expect(component.errors).toContain('Role name already exists.');
    });
  });

  describe('Save Operations', () => {
    beforeEach(() => {
      fixture.detectChanges();
      component.selectedRole = { name: 'Manager', code: '1' };
      component.selectedDepartment = { name: 'Sales', code: 'Sales' };
      component.selectedUpperRole = { name: 'Admin', code: '2' };
      component.roleDescription = 'Updated description';
      component.workflowRowData = [
        { taskTypeId: 'task1', task: 'Review Task', attend: true },
        { taskTypeId: 'task2', task: 'Approval Task', attend: false }
      ];
    });

    it('should save data successfully when validation passes', () => {
      jest.spyOn(component as any, 'validateData').mockReturnValue(true);

      component['saveData']();

      expect(mockRoleService.updateCapabilities).toHaveBeenCalledWith({
        roleId: '1',
        name: 'Manager',
        department: 'Sales',
        superiorId: '2',
        capabilities: component.rowData,
        tasks: [{ roleId: '1', taskId: 'task1' }],
        description: 'Updated description'
      });
      expect(mockToastService.displaySuccess).toHaveBeenCalledWith('Role saved successfully.');
      expect(mockEditingStateService.setValue).toHaveBeenCalledWith(FormAction.SUBMIT);
    });

    it('should not save data when validation fails', () => {
      jest.spyOn(component as any, 'validateData').mockReturnValue(false);

      component['saveData']();

      expect(mockRoleService.updateCapabilities).not.toHaveBeenCalled();
    });

    it('should handle save error', () => {
      jest.spyOn(component as any, 'validateData').mockReturnValue(true);
      mockRoleService.updateCapabilities.mockReturnValue(throwError('Save error'));

      component['saveData']();

      expect(mockToastService.displayError).toHaveBeenCalledWith('Failed while saving Role data.');
    });

    it('should include only attended tasks in save data', () => {
      jest.spyOn(component as any, 'validateData').mockReturnValue(true);
      component.workflowRowData = [
        { taskTypeId: 'task1', task: 'Review Task', attend: true },
        { taskTypeId: 'task2', task: 'Approval Task', attend: false },
        { taskTypeId: 'task3', task: 'Final Task', attend: true }
      ];

      component['saveData']();

      const saveCall = mockRoleService.updateCapabilities.mock.calls[0][0];
      expect(saveCall.tasks).toEqual([
        { roleId: '1', taskId: 'task1' },
        { roleId: '1', taskId: 'task3' }
      ]);
    });
  });

  describe('Editing State Integration', () => {
    it('should handle EDIT action', () => {
      component.rowData = mockRole.capabilities || [];
      component.selectedRole = { name: 'Manager', code: '1' };
      component.upperRoles = [{ name: 'Admin', code: '2' }];

      const editingStateSubject = new Subject<FormAction>();
      mockEditingStateService.getValue.mockReturnValue(editingStateSubject.asObservable());

      // Re-create component to trigger constructor subscription
      fixture = TestBed.createComponent(RoleDetailsComponent);
      component = fixture.componentInstance;
      component.rowData = mockRole.capabilities || [];
      component.selectedRole = { name: 'Manager', code: '1' };
      component.upperRoles = [{ name: 'Admin', code: '2' }];

      editingStateSubject.next(FormAction.EDIT);

      expect(mockEditingStateService.setData).toHaveBeenCalledWith([
        ['rowData', JSON.parse(JSON.stringify(component.rowData))]
      ]);
      expect(mockEditingStateService.setData).toHaveBeenCalledWith([
        ['selectedRole', JSON.parse(JSON.stringify(component.selectedRole))]
      ]);
      expect(mockEditingStateService.setData).toHaveBeenCalledWith([
        ['upperRoles', JSON.parse(JSON.stringify(component.upperRoles))]
      ]);
    });

    it('should handle SAVE action', () => {
      jest.spyOn(component as any, 'saveData').mockImplementation(() => {});
      const editingStateSubject = new Subject<FormAction>();
      mockEditingStateService.getValue.mockReturnValue(editingStateSubject.asObservable());

      // Re-create component to trigger constructor subscription
      fixture = TestBed.createComponent(RoleDetailsComponent);
      component = fixture.componentInstance;

      editingStateSubject.next(FormAction.SAVE);

      expect(component['saveData']).toHaveBeenCalled();
    });

    it('should handle CANCEL action', () => {
      const editingStateSubject = new Subject<FormAction>();
      mockEditingStateService.getValue.mockReturnValue(editingStateSubject.asObservable());

      // Re-create component to trigger constructor subscription
      fixture = TestBed.createComponent(RoleDetailsComponent);
      component = fixture.componentInstance;

      editingStateSubject.next(FormAction.CANCEL);

      // Should trigger data reset
      expect(component.rowData).toBeDefined();
    });
  });

  describe('Data Reset Operations', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should reset row data correctly', () => {
      component.defRowData = [
        { component: ['admin'], view: true, edit: false, create: false, delete: false }
      ];

      component['resetRowData']();

      expect(component.rowData).toEqual(component.defRowData);
    });
  });

  describe('Component Lifecycle', () => {
    it('should complete destroy$ subject on ngOnDestroy', () => {
      const destroySpy = jest.spyOn(component['destroy$'], 'next');
      const completeSpy = jest.spyOn(component['destroy$'], 'complete');

      component.ngOnDestroy();

      expect(destroySpy).toHaveBeenCalled();
      expect(completeSpy).toHaveBeenCalled();
    });

    it('should unsubscribe from observables on destroy', () => {
      const destroySpy = jest.spyOn(component['destroy$'], 'next');

      component.ngOnDestroy();

      expect(destroySpy).toHaveBeenCalled();
    });
  });

  describe('Edge Cases', () => {
    it('should handle role without capabilities', () => {
      const roleWithoutCapabilities = { ...mockRole, capabilities: [] };
      mockActivatedRoute.data = of({ data: roleWithoutCapabilities });

      // Re-create component to trigger new data loading
      fixture = TestBed.createComponent(RoleDetailsComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();

      expect(component.rowData).toBeDefined();
    });

    it('should handle role without superior', () => {
      const roleWithoutSuperior = { ...mockRole, superior: null, superiorId: null };
      mockActivatedRoute.data = of({ data: roleWithoutSuperior });

      // Re-create component to trigger new data loading
      fixture = TestBed.createComponent(RoleDetailsComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();

      expect(component.selectedUpperRole).toEqual({
        code: null,
        name: undefined
      });
    });

    it('should handle workflow tasks without system workflows', () => {
      const nonSystemWorkflows = mockWorkflowTaskTypes.map(wt => ({
        ...wt,
        workflow: {
          workflowType: {
            createdBySystem: false
          }
        }
      }));
      mockWorkflowService.findWorkflowTaskTypes.mockReturnValue(of(nonSystemWorkflows));

      // Re-create component to trigger new data loading
      fixture = TestBed.createComponent(RoleDetailsComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();

      expect(component.workflowRowData).toEqual([]);
    });

    it('should handle empty roles list', () => {
      mockRoleService.findAll.mockReturnValue(of([]));

      // Re-create component to trigger new data loading
      fixture = TestBed.createComponent(RoleDetailsComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();

      expect(component.roles).toEqual([]);
      expect(component.departmentList).toEqual([]);
      expect(component.treeRoles).toEqual([]);
    });

    it('should handle department as string instead of object', () => {
      component.selectedDepartment = 'Sales';
      jest.spyOn(component as any, 'validateData').mockReturnValue(true);

      component['saveData']();

      const saveCall = mockRoleService.updateCapabilities.mock.calls[0][0];
      expect(saveCall.department).toBe('Sales');
    });
  });

  describe('Tree Role Operations', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should read tree role children correctly', () => {
      const parentRole: any = { name: 'Admin', code: '2', label: 'Admin' };
      const childRole = { ...mockRoles[0], superior: mockRoles[1] }; // Manager reports to Admin
      component.roles = [childRole, mockRoles[1]];

      component['readTreeRoleChildren'](parentRole);

      expect(parentRole.children).toBeDefined();
      expect(parentRole.children.length).toBe(1);
      expect(parentRole.children[0].name).toBe('Manager');
    });

    it('should handle roles without children', () => {
      const leafRole: any = { name: 'Employee', code: '3', label: 'Employee' };
      component.roles = [mockRoles[2]]; // Employee has no children

      component['readTreeRoleChildren'](leafRole);

      expect(leafRole.children).toBeUndefined();
    });
  });
});
